// 测试VIO分组聚合功能
function testVioAggregation() {
  // 模拟测试数据
  const testData = [
    { source: '中国', vio: 100 },
    { source: '中国', vio: 200 },
    { source: '中国', vio: 300 },
    { source: '俄罗斯', vio: 150 },
    { source: '俄罗斯', vio: 250 },
    { source: '美国', vio: 400 },
    { source: null, vio: 50 }, // 测试空值处理
    { source: '德国', vio: null }, // 测试vio为空的情况
  ]

  // VIO分组聚合逻辑
  function aggregateVioBySource(data) {
    if (!data.length) return []
    
    // 按source字段分组，聚合vio值
    const groupMap = new Map()
    
    data.forEach(item => {
      const source = item.source || '未知来源'
      const vio = parseInt(item.vio) || 0
      
      if (groupMap.has(source)) {
        groupMap.set(source, groupMap.get(source) + vio)
      } else {
        groupMap.set(source, vio)
      }
    })
    
    // 转换为数组格式并排序
    return Array.from(groupMap.entries())
      .map(([source, totalVio]) => ({ source, totalVio }))
      .sort((a, b) => b.totalVio - a.totalVio) // 按VIO总数降序排列
  }

  // 执行测试
  const result = aggregateVioBySource(testData)
  
  console.log('测试数据:', testData)
  console.log('聚合结果:', result)
  
  // 验证结果
  const expected = [
    { source: '中国', totalVio: 600 },
    { source: '俄罗斯', totalVio: 400 },
    { source: '美国', totalVio: 400 },
    { source: '未知来源', totalVio: 50 },
    { source: '德国', totalVio: 0 }
  ]
  
  console.log('期望结果:', expected)
  
  // 简单验证
  const isCorrect = result.length === expected.length &&
    result[0].source === '中国' && result[0].totalVio === 600 &&
    result[1].totalVio === 400 // 俄罗斯和美国都是400，顺序可能不同
  
  console.log('测试结果:', isCorrect ? '✅ 通过' : '❌ 失败')
  
  return result
}

// 运行测试
testVioAggregation()
