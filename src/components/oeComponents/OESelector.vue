<script setup>
import { ref, onMounted, computed } from 'vue'
import api from '@/api'
import {
  NButton,
  NDataTable,
  NFormItem,
  NGi,
  NGrid, NInput,
  NPagination,
  NSelect
} from 'naive-ui'
const emit = defineEmits(['select']);
// 获取初始选项
const oeCarIds = ref([]) // 自动绑定的车型reach_car_ids
const loading = ref(false)
const props = defineProps({
  show: Boolean,
  category: String,
  selectCarIdsFromIndex: Array // 来自主页的车型id数据
});
// 监听 props.category 变化
watch(
  () => props.category,
  (newVal) => {
    if (newVal) {
      queryItems.value.category = newVal
    }
  },
  { immediate: true } // 立即执行一次，确保初始值也能设置
)

// 监听 props.selectCarIdsFromIndex 变化
watch(
  () => props.selectCarIdsFromIndex,
  (newVal) => {
    if (newVal) {
      props.selectCarIdsFromIndex = newVal
    }
  },
  { immediate: true } // 立即执行一次，确保初始值也能设置
)
const showModal = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});
const tableData = ref([])
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
})

/*---选项属性---*/
const queryItems = ref({
  oes: null,
  category:props.category,
})
const columns = [
  {
    type: 'selection',
  },
  {
    title: '唯一id',
    key: '_id',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: 'OE号',
    key: 'Brand_Number',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: 'Reach产品号',
    key: 'Reach_Number',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '主号',
    key: 'Number',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
]


const selectedIds = ref([]); // 只能选择一个

const safeSelectedIds = computed(() => {
  console.log("------->选择的selectedIds为", selectedIds)
  console.log("------->zhende", Array.isArray(selectedIds.value))
  return Array.isArray(selectedIds.value) ? selectedIds.value : []
})

// 当选中项变化时，只保留最后一个点击的
function handleUpdateCheckedRowKeys(keys) {
  if (keys.length === 0) {
    selectedIds.value = []
  } else {
    // 始终只保留最后一个选中的（即最新点击的）
    selectedIds.value = [keys[keys.length - 1]]
  }
}


/*----------获取OE数据------------*/
// 获取OE列表
const fetchMainNumData = async () => {
  // 根据OEs号、品类查询现在存在的产品(重点获取主号)。
  const params = {
    ...queryItems.value,
    page: pagination.value.page,
    pageSize: pagination.value.pageSize,
    timeout: 30000 // 30秒
  }
  console.log("---------->queryItems为", queryItems.value)
  console.log("---------->category为", queryItems.value.category)
  const res = await api.searchOEForComponentMainNum(params)
  console.log("**************OE组件的返回值******************", res.data)
  tableData.value = res.data || [];
  pagination.value.itemCount = Number(res.total) || 0;
}


// 切换页面触发
function handlePageChange(page) {
  pagination.value.page = page
  fetchMainNumData()
}

// 调整页面大小触发
function handlePageSizeChange(pageSize) {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  fetchMainNumData()
}

// 条件查询触发
async function handleSearch() {
  pagination.value.page = 1
  await fetchMainNumData()
  console.log("------------->tableData数据为", tableData)
}

const confirmSelection = async () => {
  const searchReachCarIds = ref([])
  const reachCarInfosToIndex = ref([])
  // ✅ 1. 获取选中的行数据
  const selectedRows = tableData.value.filter(row =>
    selectedIds.value.includes(row._id)
  );
  console.log("----------->selectedRows为", selectedRows[0].Number)

  // ✅ 3. 获取该主号，下面所有产品的车型（包含了替换号的）
  if (Array.isArray(selectedRows) && selectedRows.length > 0) {
    const selectMainNum = selectedRows[0].Number
    // 从tableData中获取主号对应的产品reach号，避免直接查询数据库查询出不必要的数据
    const relationReachNumList = tableData.value.filter(item => item['Number'] === selectMainNum).map(item => item['Reach_Number'])
    console.log("---------->relationReachNumList为：", relationReachNumList)
    // 根据所有的产品号，查询其全部车型reach_car_id
    const getReachCarIdsResponse = await api.getReachCarIdsByReachNumber({ "reachNumbersList": relationReachNumList })
    searchReachCarIds.value = getReachCarIdsResponse.data.map(item => item.reach_car_id)
    // 如果主页有车型数据了，直接将这个reach_car_id去掉就行
    console.log("----------->现在的车型数据为：", props.selectCarIdsFromIndex)
    // 根据所有reach_car_id进行查询车型信息，在此之前需要先去掉来自主页的selectCar中选择的车型数据
    const selectedCarIdsIndex = props.selectCarIdsFromIndex.map(item => item.reach_car_id)
    oeCarIds.value = searchReachCarIds.value.filter(carId =>
      !selectedCarIdsIndex.includes(carId)
    ) // oeCarIds是排除主页手动选择的车型的结果
    // 去后端查询这些carIds的车型信息
    const getCarInfoByReachCarIdsResponse = await api.getCarInfoByReachCarIds({"reachCarIds":oeCarIds.value})
    console.log("-------->getCarInfoByReachCarIdsResponse为：",getCarInfoByReachCarIdsResponse)
    // reachCarInfosToIndex = 主页手动选择的车型数据 + 自动绑定的车型数据。并发给主页
    reachCarInfosToIndex.value = [...props.selectCarIdsFromIndex, ...getCarInfoByReachCarIdsResponse.data]
    console.log("------------>弹窗向主页发送的车型数据reachCarInfosToIndex为：", reachCarInfosToIndex)
  }

  // ✅ 2. 通过 emit 把数据传出去（可选）
  emit('select', {
    selectedRows: selectedRows, // 完整数据
    reachCarInfosToIndex: reachCarInfosToIndex, // 将全部的车型数据发送给主页
    oeCarIds: oeCarIds.value // 这是自动绑定的车型数据的ids
  });


  // ✅ 3. 关闭弹窗
  showModal.value = false;
};

watch(selectedIds, (newVal) => {
  // if (!Array.isArray(newVal)) {
  //   console.warn('selectedIds 被设为非数组，重置为空数组')
  //   selectedIds.value = []
  // }
}, { deep: true })


onMounted(() => {
  // fetchMainNumData();
});
</script>

<template>
  <!-- 业务页面 -->
  <n-modal v-model:show="showModal" style="width: 900px;">
    <n-card title="选择适配OE" :bordered="false" size="huge">
      <!-- 表格 -->
      <n-grid :cols="20" :x-gap="12" class="mb-4">
        <n-gi :span="12">
          <n-form-item label="OE搜索" :label-width="60" label-placement="left">
            <n-input
              v-model:value="queryItems.oes"
              placeholder="请输入OE号"
              clearable
              :disabled="loading"
            />
          </n-form-item>
        </n-gi>

        <n-gi :span="4">
          <n-button type="primary" @click="handleSearch">搜索</n-button>
<!--          <n-button class="ml-2" @click="customReset">重置</n-button>-->
        </n-gi>
      </n-grid>

      <br />
      <!-- 数据表格 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :bordered="true"
        :single-line="true"
        :row-key="row => row._id"
        :checked-row-keys="selectedIds ? selectedIds : []"
        @update:checked-row-keys="handleUpdateCheckedRowKeys"
        class="flex-1"
      />

      <!-- 分页 -->
      <n-pagination
        :item-count="pagination.itemCount"
        :page-sizes="pagination.pageSizes"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
      <template #footer>
        <div style="text-align: right;">
          <n-button
            type="primary"
            @click="confirmSelection"
          >
            确认选择 ({{ safeSelectedIds.length }})
          </n-button>
          <n-button class="ml-2" @click="showModal = false">取消</n-button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped>

</style>