<script setup>
import { ref, onMounted, computed } from 'vue'
import api from '@/api'
import {
  NButton,
  NDataTable,
  NFormItem,
  NGi,
  NGrid,
  NPagination,
  NSelect
} from 'naive-ui'
import QuerySelect from '@/components/query-bar/QuerySelect.vue'
import CommonPage from '@/components/page/CommonPage.vue' // 假设你有这个 API
const emit = defineEmits(['select']);
// 获取初始选项
const loading = ref(false)
const datetimeRange = ref(null)
const tabOptions = ref(['全部', '中国', '北美', '欧洲', '俄罗斯', '加拿大', '墨西哥', '东南亚'])
const props = defineProps({
  show: Boolean,
  defaultSelectedIds: {
    type: Array,
    default: () => []
  }
});
const showModal = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});

/*---选项属性---*/
const querySelectRef = ref(null)
const vehicle_type_options = ref([])
const brand_options = ref([])
const series_options = ref([])
const year_options = ref([])
const queryItems = ref({
  country: null,
  vehicle_type: null,
  brand: null,
  series: null,
  model_year: null,
})
/*---车型属性---*/
const brand = ref('');
const model = ref('');
const tableData = ref([])
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
})
const columns = [
  {
    type: 'selection',
  },
  {
    title: 'reachVehicleId',
    key: 'reach_car_id',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '品牌',
    key: 'brand',
    resizable: true,
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '厂家',
    key: 'manufacturer',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型分类',
    key: 'vehicle_type',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车系',
    key: 'series',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '车型',
    key: 'model',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
  {
    title: '年款',
    key: 'model_year',
    resizable: true,
    align: 'center',
    width: 'auto',
    ellipsis: { tooltip: true },
  },
]




const selectedIds = ref([...props.defaultSelectedIds]);

const safeSelectedIds = computed(() => {
  return Array.isArray(selectedIds.value) ? selectedIds.value : []
})


/*----------条件查询选项------------*/
// 处理选项的主逻辑
const handleOptionChange = async (optionKey, selectedValue) => {
  loading.value = true
  try {
    queryItems.value[optionKey] = selectedValue
    // 从后端获取并更新其它选项
    const updatedOptions = await api.getOptionList(queryItems.value)
    // 更新选项
    vehicle_type_options.value = updatedOptions.data.vehicle_type || []
    brand_options.value = updatedOptions.data.brand || []
    series_options.value = updatedOptions.data.series || []
    year_options.value = updatedOptions.data.model_year || []
  } catch (error) {
    console.error('获取选项失败', error)
  } finally {
    loading.value = false
  }
}

async function customReset() {
  datetimeRange.value = null
  // 清空 queryItems 的内容
  queryItems.value = {
    country: null,
    vehicle_type: null,
    brand: null,
    series: null,
    model_year: null,
    displacement: null,
    trans_type: null,
    fuel_type: null,
    start_time: null,
    end_time: null,
  }
  // 重置 QuerySelect 组件状态
  if (querySelectRef.value) {
    querySelectRef.value.selectTab(tabOptions.value[0]) // 选中"全部"
  }
  // 重新填充所有下拉框的数据
  await fetchInitOptions()
  await handleSearch()
}

const fetchInitOptions = async () => {
  loading.value = true
  try {
    const api_options = await api.getOptionList()
    vehicle_type_options.value = api_options.data.vehicle_type || []
    brand_options.value = api_options.data.brand || []
    series_options.value = api_options.data.series || []
    year_options.value = api_options.data.model_year || []
  } catch (error) {
    console.error('获取选项失败', error)
  } finally {
    loading.value = false // 完成加载
  }
}



/*----------获取车型数据------------*/

// 获取车型列表
const fetchData = async () => {
  const params = {
    ...queryItems.value,
    page: pagination.value.page,
    pageSize: pagination.value.pageSize,
  }
  const res = await api.getCarsList(params)
  tableData.value = res.data || [];
  pagination.value.itemCount = Number(res.total) || 0;
};


// 切换页面触发
function handlePageChange(page) {
  pagination.value.page = page
  fetchData()
}

// 调整页面大小触发
function handlePageSizeChange(pageSize) {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  fetchData()
}

// 条件查询触发
async function handleSearch() {
  pagination.value.page = 1
  await fetchData()
}

const confirmSelection = () => {
  // ✅ 1. 获取选中的行数据
  const selectedRows = tableData.value.filter(row =>
    selectedIds.value.includes(row.reach_car_id)
  );

  // ✅ 2. 提取需要的字段（比如品牌、车系、车型）
  // const selectedModels = selectedRows.map(row => row.model).join(', ');
  // const selectedSeries = selectedRows.map(row => row.series).join(', ');

  // ✅ 3. 通过 emit 把数据传出去（可选）
  emit('select', {
    // ids: selectedIds.value,
    // models: selectedModels,
    // series: selectedSeries,
    rows: selectedRows // 完整数据
  });

  // ✅ 4. 关闭弹窗
  showModal.value = false;

  // ✅ 5. （可选）在父组件中更新 formData
  // 这个逻辑可以在父组件的 @select 中处理
};

watch(selectedIds, (newVal) => {
  if (!Array.isArray(newVal)) {
    console.warn('selectedIds 被设为非数组，重置为空数组')
    selectedIds.value = []
  }
}, { deep: true })


onMounted(() => {
  fetchInitOptions();
  fetchData();
});
</script>

<template>
  <!-- 业务页面 -->
  <n-modal v-model:show="showModal" style="width: 900px;">
    <n-card title="选择适配车型" :bordered="false" size="huge">
    <!-- 表格 -->
    <n-grid :cols="20" :x-gap="12" class="mb-4">
      <n-gi :span="6">
        <n-form-item label="车型分类" :label-width="60" label-placement="left">
          <n-select
            :options="vehicle_type_options"
            placeholder="车型分类"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('vehicle_type', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="6">
        <n-form-item label="品牌" :label-width="60" label-placement="left">
          <n-select
            :options="brand_options"
            placeholder="品牌"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('brand', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="6">
        <n-form-item label="车系" :label-width="60" label-placement="left">
          <n-select
            :options="series_options"
            placeholder="车系"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('series', v)"
          />
        </n-form-item>
      </n-gi>
      <n-gi :span="6">
        <n-form-item label="年款" :label-width="60" label-placement="left">
          <n-select
            :options="year_options"
            placeholder="年款"
            filterable
            clearable
            @update:value="(v) => handleOptionChange('model_year', v)"
          />
        </n-form-item>
      </n-gi>

      <n-gi :span="4">
        <n-button type="primary" @click="handleSearch">搜索</n-button>
        <n-button class="ml-2" @click="customReset">重置</n-button>
      </n-gi>
    </n-grid>

    <br />
    <!-- 数据表格 -->
    <n-data-table
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :single-line="false"
      :row-key="row => row.reach_car_id"
      v-model:checked-row-keys="selectedIds"
      class="flex-1"
    />

    <!-- 分页 -->
    <n-pagination
      :item-count="pagination.itemCount"
      :page-sizes="pagination.pageSizes"
      @update:page="handlePageChange"
      @update:page-size="handlePageSizeChange"
    />
      <template #footer>
        <div style="text-align: right;">
          <n-button
            type="primary"
            @click="confirmSelection"
          >
            确认选择 ({{ safeSelectedIds.length }})
          </n-button>
          <n-button class="ml-2" @click="showModal = false">取消</n-button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<style scoped>

</style>