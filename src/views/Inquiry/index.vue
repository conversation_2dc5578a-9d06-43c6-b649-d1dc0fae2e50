<template>
  <div class="product-search-container">
    <n-h1 class="header">
      <n-gradient-text type="info" size="28">询价</n-gradient-text>
    </n-h1>

    <div class="search-section">
      <div class="search-controls">
        <n-select
          v-model:value="searchType"
          :options="searchOptions"
          placeholder="选择搜索类型"
          class="type-selector"
          size="large"
        />
        <div v-if="searchType === 'image'" class="upload-section" style="width: 80%">
          <n-upload
            multiple
            v-model:file-list="fileList"
            class="image-uploader"
            :show-file-list="false"
            @change="handleFileChange"
          >
            <n-upload-dragger>
              <n-text style="font-size: 16px">点击或拖拽图片到此处上传</n-text>
              <n-p depth="3" style="margin: 8px 0 0 0">支持上传多个图片文件</n-p>
            </n-upload-dragger>
          </n-upload>
        </div>
        <div v-else style="width: 80%">
          <n-input
            v-model:value="searchContent"
            type="textarea"
            placeholder="请输入搜索内容，多个搜索项用换行分隔"
            :autosize="{ minRows: 3, maxRows: 6 }"
            class="content-input"
            size="large"
          />
        </div>
        <n-button type="primary" size="large" class="search-btn" @click="handleSearch">
          <template #icon>
            <n-icon>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                <path fill="none" d="M0 0h24v24H0z" />
                <path
                  d="M18.031 16.617l4.283 4.282-1.415 1.415-4.282-4.283A8.96 8.96 0 0 1 11 20c-4.968 0-9-4.032-9-9s4.032-9 9-9 9 4.032 9 9a8.96 8.96 0 0 1-1.969 5.617zm-2.006-.742A6.977 6.977 0 0 0 18 11c0-3.868-3.133-7-7-7-3.868 0-7 3.132-7 7 0 3.867 3.132 7 7 7a6.977 6.977 0 0 0 4.875-1.975l.15-.15z"
                />
              </svg>
            </n-icon>
          </template>
          搜索
        </n-button>
      </div>
    </div>

    <!-- 批量操作控件 -->
    <div v-if="showResults" class="batch-controls">
      <n-select
        v-model:value="batchProductLineValue"
        :options="productlineOptions"
        placeholder="批量设置产品线"
        style="width: 200px; margin-right: 10px"
      />
      <n-button type="primary" @click="handleBatchUpdate"> 应用到选中项 </n-button>
    </div>

    <!-- 图片搜索结果表格 -->
    <div v-if="showResults && searchType === 'image'" class="result-section">
      <n-card title="搜索结果" :bordered="false" class="result-card">
        <div class="table-container">
          <n-data-table
            :columns="imageColumns"
            :data="imageTableData"
            :bordered="false"
            :striped="true"
            size="large"
            :row-key="(row) => row.id"
            :checked-row-keys="checkedImageRowKeys"
            @update:checked-row-keys="handleImageCheck"
            :scroll-x="1200"
          />
        </div>
      </n-card>
    </div>

    <!-- 文本搜索结果表格 -->
    <div v-if="showResults && searchType !== 'image'" class="result-section">
      <n-card title="搜索结果" :bordered="false" class="result-card">
        <div class="table-container">
          <n-data-table
            :columns="columns"
            :data="tableData"
            :bordered="false"
            :striped="true"
            size="large"
            :row-key="(row) => row.id"
            :checked-row-keys="checkedRowKeys"
            @update:checked-row-keys="handleCheck"
            :scroll-x="1200"
          />
        </div>
      </n-card>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, h } from 'vue'
import {
  NH1,
  NGradientText,
  NSelect,
  NInput,
  NButton,
  NIcon,
  NUpload,
  NUploadDragger,
  NText,
  NP,
  NCard,
  NDataTable,
  NImage,
  NPopover,
} from 'naive-ui'
import api from '@/api'

export default defineComponent({
  components: {
    NH1,
    NGradientText,
    NSelect,
    NInput,
    NButton,
    NIcon,
    NUpload,
    NUploadDragger,
    NText,
    NP,
    NCard,
    NDataTable,
    NImage,
    NPopover,
  },
  setup() {
    const searchType = ref('oe')
    const searchOptions = [
      { label: 'OE号', value: 'oe' },
      { label: '竞品', value: 'competitor' },
      { label: '产品', value: 'product' },
      { label: '车型', value: 'model' },
      { label: '图片&图纸', value: 'image' },
    ]

    const searchContent = ref('')
    const fileList = ref([])
    const showResults = ref(false)
    const tableData = ref([])
    const imageTableData = ref([])
    const checkedRowKeys = ref([]) // 文本表格选中项
    const checkedImageRowKeys = ref([]) // 图片表格选中项
    const batchProductLineValue = ref(null)
    const productlineOptions = ref([])

    // 在组件挂载时获取产品线数据
    onMounted(async () => {
      try {
        const response = await api.getProductLine()
        // 将API响应转换为选项格式
        productlineOptions.value = response.data.map((item) => ({
          label: item.name_cn,
          value: item.name_cn,
        }))
      } catch (error) {
        console.error('获取产品线数据失败:', error)
        window.$message?.error('获取产品线数据失败')
      }
    })

    // 图片搜索结果的列定义
    const imageColumns = [
      {
        type: 'selection',
        fixed: 'left',
        width: 40,
      },
      {
        title: '图片',
        key: 'image',
        width: 80,
        render(row) {
          return h(
            NPopover,
            {
              trigger: 'hover',
              placement: 'right',
              style: { Width: '80px' },
            },
            {
              trigger: () =>
                h(NImage, {
                  width: 80,
                  src: row.url,
                  style: { cursor: 'pointer' },
                }),
              default: () =>
                h(NImage, {
                  width: 180,
                  src: row.url,
                }),
            }
          )
        },
      },
      {
        title: '文件名',
        key: 'name',
        width: 150,
      },
      {
        title: '产品线',
        key: 'productline',
        width: 200,
        render(row) {
          return h(NSelect, {
            value: row.productline,
            onUpdateValue: (value) => {
              row.productline = value
            },
            options: productlineOptions.value,
            size: 'small',
            placeholder: '选择产品线',
            style: 'width: 200px;',
          })
        },
      },
      {
        title: '操作',
        key: 'actions',
        width: 30,
        render(row) {
          return h(
            NButton,
            {
              size: 'small',
              type: 'error',
              onClick: () => handleDeleteImage(row.id),
            },
            {
              default: () => '删除',
            }
          )
        },
      },
    ]

    // 文本搜索结果的列定义
    const columns = [
      {
        type: 'selection',
        fixed: 'left',
        width: 20,
      },
      {
        title: '搜索项',
        key: 'term',
        width: 100,
      },
      {
        title: '搜索类型',
        key: 'type',
        width: 100,
        render(row) {
          return searchOptions.find((opt) => opt.value === row.type)?.label || row.type
        },
      },
      {
        title: '品牌',
        key: 'brand',
        width: 150,
      },
      {
        title: '产品编码',
        key: 'result',
        width: 150,
      },
      {
        title: '价格',
        key: 'price',
        width: 50,
      },
      {
        title: '产品线',
        key: 'productline',
        width: 200,
        render(row) {
          return h(NSelect, {
            value: row.productline,
            onUpdateValue: (value) => {
              row.productline = value
            },
            options: productlineOptions.value,
            size: 'small',
            placeholder: '选择产品线',
            style: 'width: 200px;',
          })
        },
      },
      {
        title: '操作',
        key: 'actions',
        width: 200,
        render(row) {
          return h(
            NButton,
            {
              size: 'small',
              type: 'error',
              onClick: () => handleDelete(row.id),
            },
            {
              default: () => '删除',
            }
          )
        },
      },
    ]

    // 处理图片表格行选择
    const handleImageCheck = (keys) => {
      checkedImageRowKeys.value = keys
    }

    // 处理文本表格行选择
    const handleCheck = (keys) => {
      checkedRowKeys.value = keys
    }

    // 处理图片删除
    const handleDeleteImage = (id) => {
      const index = imageTableData.value.findIndex((item) => item.id === id)
      if (index !== -1) {
        imageTableData.value.splice(index, 1)
        // 从选中项中移除
        const keyIndex = checkedImageRowKeys.value.indexOf(id)
        if (keyIndex !== -1) {
          checkedImageRowKeys.value.splice(keyIndex, 1)
        }
        // 同时从文件列表中删除
        fileList.value = fileList.value.filter((file) => file.id !== id)
      }
    }

    // 处理文本行删除
    const handleDelete = (id) => {
      const index = tableData.value.findIndex((item) => item.id === id)
      if (index !== -1) {
        tableData.value.splice(index, 1)
        const keyIndex = checkedRowKeys.value.indexOf(id)
        if (keyIndex !== -1) {
          checkedRowKeys.value.splice(keyIndex, 1)
        }
      }
    }

    // 批量更新状态
    const handleBatchUpdate = () => {
      if (!batchProductLineValue.value) {
        window.$message?.warning('请选择要设置的状态')
        return
      }

      if (searchType.value === 'image') {
        // 更新图片表格选中项
        if (checkedImageRowKeys.value.length === 0) {
          window.$message?.warning('请至少选择一张图片')
          return
        }

        imageTableData.value.forEach((item) => {
          if (checkedImageRowKeys.value.includes(item.id)) {
            item.productline = batchProductLineValue.value
          }
        })

        window.$message?.success(`已更新${checkedImageRowKeys.value.length}行的状态`)
      } else {
        // 更新文本表格选中项
        if (checkedRowKeys.value.length === 0) {
          window.$message?.warning('请至少选择一行')
          return
        }

        tableData.value.forEach((item) => {
          if (checkedRowKeys.value.includes(item.id)) {
            item.productline = batchProductLineValue.value
          }
        })

        window.$message?.success(`已更新${checkedRowKeys.value.length}行的状态`)
      }
    }

    // 处理搜索
    const handleSearch = () => {
      if (searchType.value === 'image') {
        if (fileList.value.length === 0) {
          window.$message?.warning('请上传至少一张图片文件')
          return
        }

        // 生成图片搜索结果数据
        imageTableData.value = fileList.value.map((file, index) => ({
          id: file.id,
          number: index + 1,
          name: file.name,
          url: 'https://dummyimage.com/200x150/007bff/ffffff&text=Product+Image',
          /* productline: 'processing', */
        }))

        showResults.value = true
        checkedImageRowKeys.value = [] // 清空图片表格选中项
        return
      }

      if (!searchContent.value.trim()) {
        window.$message?.warning('请输入搜索内容')
        return
      }

      const terms = searchContent.value
        .split(/[\n\/,; ]+/)
        .filter((term) => term.trim())
        .map((term) => term.trim())
        .filter((term, index, self) => self.indexOf(term) === index)

      fetchInquiryResults(searchType.value, terms)
      showResults.value = true
      checkedRowKeys.value = []
    }

    async function fetchInquiryResults(type, terms) {
      try {
        const newTableData = []

        if (type == 'product') {
          // 调用接口并传入terms
          const response = await api.getInquiry({
            type: type,
            w_list: terms,
          })
          const results = response.data
          // console.log(results)
          terms.forEach((term, index) => {
            const item = results.find((item) => item.MaterialCode === term)
            newTableData.push({
              id: Date.now() + index,
              term: term,
              type: searchType.value,
              brand: '',
              result: item ? item.MaterialCode : '未搜索到产品',
              price: '100',
              productline: item ? item.ChineseName : '未找到产品线',
            })
          })
        }
        if (type == 'oe' || type == 'competitor') {
          // 调用接口并传入terms
          const response = await api.getInquiry({
            type: type, // 修正字符串闭合和逗号分隔
            w_list: terms, // 确保键名与后端匹配
          })
          const results = response.data

          // 创建以Brand_Number为键的映射，值是对应所有结果的数组
          const resultMap = new Map()
          results.forEach((item) => {
            if (item && item.Brand_Number) {
              const brandNumber = item.Brand_Number
              if (!resultMap.has(brandNumber)) {
                resultMap.set(brandNumber, [])
              }
              resultMap.get(brandNumber).push(item)
            }
          })

          // 生成表格数据 - 为每个匹配项创建单独行
          terms.forEach((term, index) => {
            const items = resultMap.get(term) || []

            if (items.length === 0) {
              // 无匹配结果时创建单行
              newTableData.push({
                id: Date.now() + index,
                term: term,
                type: searchType.value,
                brand: '未找到品牌',
                result: '未搜索到产品',
                price: '100',
                productline: '未找到产品线',
              })
            } else {
              // 为每个匹配项创建单独行
              items.forEach((item, itemIndex) => {
                newTableData.push({
                  id: Date.now() + index + itemIndex, // 确保ID唯一
                  term: term,
                  type: searchType.value,
                  brand: item.Type || '未找到品牌',
                  result: item.Reach_Number || '无结果',
                  price: '100', // 根据实际需求调整
                  productline: item.Standard_Name || '未找到产品线',
                })
              })
            }
          })
        }
        tableData.value = newTableData
      } catch (error) {
        console.error('获取询价结果失败', error)
      }
    }

    return {
      searchType,
      searchOptions,
      searchContent,
      fileList,
      showResults,
      tableData,
      imageTableData,
      columns,
      imageColumns,
      handleSearch,
      checkedRowKeys,
      checkedImageRowKeys,
      batchProductLineValue,
      productlineOptions,
      handleCheck,
      handleImageCheck,
      handleDelete,
      handleDeleteImage,
      handleBatchUpdate,
    }
  },
})
</script>

<style scoped>
.product-search-container {
  max-width: 1350px;
  margin: 0 auto;
  padding: 30px;
  font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  height: calc(100vh - 60px); /* 固定高度 */
  /*overflow-y: auto;  添加垂直滚动条 */
}

.search-section {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  padding: 20px;
  margin-bottom: 10px;
}

.search-controls {
  display: flex;
  gap: 16px;
  margin-bottom: 15px;
  align-items: flex-start;
}

.type-selector {
  width: 180px;
  flex-shrink: 0;
}

.content-input {
  flex-grow: 1;
}

.search-btn {
  flex-shrink: 0;
  height: auto;
  padding: 0 24px;
  font-weight: bold;
}

.upload-section {
  flex-grow: 1;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 10px;
  min-height: 120px;
}

.image-uploader {
  height: 100%;
}

.result-section {
  margin-top: 10px;
}

.result-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table-container {
  height: 300px; /* 固定高度 */
  overflow-y: auto; /* 添加滚动条 */
}

.batch-controls {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: #f8f8f8;
  border-radius: 8px;
  margin-bottom: 15px;
  position: sticky; /* 固定位置 */
  top: 0; /* 固定在顶部 */
  z-index: 10; /* 确保在顶部 */
}

/* 图片预览样式 */
.image-preview {
  width: 100px;
  height: 75px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.image-preview:hover {
  transform: scale(1.05);
}

@media (max-width: 768px) {
  .product-search-container {
    padding: 15px;
    height: auto;
    overflow-y: visible;
  }

  .search-controls {
    flex-direction: column;
  }

  .type-selector,
  .upload-section,
  .content-input {
    width: 100%;
  }

  .search-btn {
    width: 100%;
  }

  .table-container {
    height: 300px; /* 移动端调整高度 */
  }
}
</style>
